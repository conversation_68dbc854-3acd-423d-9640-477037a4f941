export default function EliteMemberTestimonials() {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON> Sterling",
      title: "Tech Entrepreneur & Venture Capitalist",
      company: "Sterling Ventures",
      location: "Silicon Valley, CA",
      memberSince: "2019",
      quote: "Octavia Opulence has completely redefined what luxury means to me. The private island experience in the Maldives was beyond anything I could have imagined. The level of service, privacy, and attention to detail justifies every penny of the membership fee.",
      experience: "Private Island Maldives",
      avatar: "AS"
    },
    {
      id: 2,
      name: "Dr. <PERSON>",
      title: "Investment Banking Managing Director",
      company: "Goldman Sachs",
      location: "Hong Kong",
      memberSince: "2020",
      quote: "The network alone justifies the membership cost. I've made invaluable business connections and experienced destinations that simply aren't available to the general public. The Antarctic expedition was life-changing.",
      experience: "Antarctic Luxury Expedition",
      avatar: "MC"
    },
    {
      id: 3,
      name: "<PERSON> Blackwood",
      title: "International Fashion Designer",
      company: "Blackwood Couture",
      location: "London, UK",
      memberSince: "2018",
      quote: "The level of service and exclusivity is unmatched anywhere in the world. From private museum tours to Michelin-starred chefs on demand, Octavia delivers experiences that money typically can't buy. It's not just travel—it's transformation.",
      experience: "Versailles Private Experience",
      avatar: "RB"
    }
  ];

  const stats = [
    { number: "500", label: "Elite Members", description: "Carefully curated global community" },
    { number: "98%", label: "Satisfaction Rate", description: "Consistently exceptional experiences" },
    { number: "$2.5M", label: "Annual Value", description: "Total benefits per member" },
    { number: "24/7", label: "Concierge", description: "Always available support" }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-white via-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-block bg-gold-400/10 border border-gold-400/30 px-6 py-2 rounded-full mb-6">
            <span className="text-gold-600 text-sm font-semibold tracking-wider uppercase">
              Member Testimonials
            </span>
          </div>
          <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-8">
            Voices of
            <br />
            <span className="text-gold-600">Excellence</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Hear from our distinguished community of influential LGBTQ+ leaders, entrepreneurs, and visionaries 
            who have experienced the pinnacle of luxury travel.
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
          {testimonials.map((testimonial) => (
            <div 
              key={testimonial.id}
              className="bg-white p-8 rounded-none border-l-4 border-gold-400 shadow-xl hover:shadow-2xl transition-all duration-300 group"
            >
              {/* Header */}
              <div className="flex items-start mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                  <span className="text-white font-bold text-xl">{testimonial.avatar}</span>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-1">{testimonial.name}</h3>
                  <p className="text-gold-600 font-semibold text-sm mb-1">{testimonial.title}</p>
                  <p className="text-gray-500 text-sm mb-1">{testimonial.company}</p>
                  <p className="text-gray-400 text-xs">{testimonial.location}</p>
                </div>
              </div>

              {/* Quote */}
              <blockquote className="text-gray-700 italic text-lg leading-relaxed mb-6">
                "{testimonial.quote}"
              </blockquote>

              {/* Experience Badge */}
              <div className="mb-6">
                <span className="inline-block bg-gold-400/10 border border-gold-400/30 text-gold-600 px-4 py-2 rounded-full text-sm font-medium">
                  {testimonial.experience}
                </span>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gold-400 rounded-full mr-2"></div>
                  <span className="text-gray-500 text-sm">Member since {testimonial.memberSince}</span>
                </div>
                <div className="flex text-gold-400">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="bg-gradient-to-r from-gray-900 to-black p-12 rounded-none">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Membership by the Numbers
            </h3>
            <p className="text-xl text-gray-300">
              Excellence measured in every metric that matters
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-gold-400 mb-2">
                  {stat.number}
                </div>
                <div className="text-white font-semibold mb-1">
                  {stat.label}
                </div>
                <div className="text-gray-400 text-sm">
                  {stat.description}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
