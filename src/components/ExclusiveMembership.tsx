export default function ExclusiveMembership() {
  const benefits = [
    {
      icon: "✈️",
      title: "Private Aviation Fleet",
      description: "Unlimited access to our fleet of private jets. Skip commercial flights entirely with complete privacy and luxury.",
      value: "$500K+ Annual Value"
    },
    {
      icon: "🏰",
      title: "Exclusive Properties",
      description: "Private islands, historic castles, and ultra-luxury resorts unavailable to the public. Each property vetted for complete LGBTQ+ affirmation.",
      value: "200+ Private Venues"
    },
    {
      icon: "👑",
      title: "Elite Network Access",
      description: "Connect with influential LGBTQ+ leaders, entrepreneurs, and visionaries. Exclusive networking events in the world's most prestigious locations.",
      value: "500 Distinguished Members"
    },
    {
      icon: "🥂",
      title: "Michelin-Starred Experiences",
      description: "Private dining with world-renowned chefs, exclusive wine tastings, and culinary experiences that money alone cannot buy.",
      value: "50+ Celebrity Chefs"
    },
    {
      icon: "🎭",
      title: "Cultural Immersion",
      description: "Private museum tours after hours, exclusive art gallery openings, and cultural experiences with world-class curators.",
      value: "Global Cultural Access"
    },
    {
      icon: "💎",
      title: "24/7 Concierge",
      description: "Dedicated personal concierge team available around the clock. From impossible reservations to last-minute arrangements.",
      value: "White-Glove Service"
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-gray-900 via-black to-gray-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-block bg-gold-400/10 border border-gold-400/30 px-6 py-2 rounded-full mb-6">
            <span className="text-gold-400 text-sm font-semibold tracking-wider uppercase">
              Membership Privileges
            </span>
          </div>
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
            Beyond Luxury.
            <br />
            <span className="text-gold-400">Beyond Ordinary.</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Experience the pinnacle of luxury travel with benefits that transcend traditional first-class service. 
            Our members enjoy access to experiences that simply cannot be purchased elsewhere.
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {benefits.map((benefit, index) => (
            <div 
              key={index}
              className="group bg-gradient-to-br from-gray-800/50 to-black/50 p-8 rounded-none border border-gold-400/20 hover:border-gold-400/50 transition-all duration-500 hover:transform hover:scale-105"
            >
              {/* Icon */}
              <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                {benefit.icon}
              </div>
              
              {/* Content */}
              <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-gold-400 transition-colors">
                {benefit.title}
              </h3>
              <p className="text-gray-300 leading-relaxed mb-6">
                {benefit.description}
              </p>
              
              {/* Value Badge */}
              <div className="inline-block bg-gold-400/10 border border-gold-400/30 px-4 py-2 rounded-full">
                <span className="text-gold-400 text-sm font-semibold">
                  {benefit.value}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Value Proposition */}
        <div className="text-center bg-gradient-to-r from-gold-400/10 to-gold-600/10 border border-gold-400/20 p-12 rounded-none">
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Total Annual Value: <span className="text-gold-400">$2.5M+</span>
          </h3>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Our members receive access to experiences and services valued at over $2.5 million annually, 
            making the $25,000 membership investment an extraordinary value proposition.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <div className="text-center">
              <div className="text-3xl font-bold text-gold-400">500</div>
              <div className="text-gray-400">Global Members</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gold-400">200+</div>
              <div className="text-gray-400">Exclusive Properties</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gold-400">24/7</div>
              <div className="text-gray-400">Concierge Service</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gold-400">100%</div>
              <div className="text-gray-400">LGBTQ+ Affirming</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
