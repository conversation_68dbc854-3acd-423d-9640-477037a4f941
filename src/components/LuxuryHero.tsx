'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function LuxuryHero() {
  const [currentSlide, setCurrentSlide] = useState(0);
  
  const slides = [
    {
      title: "OCTAVIA OPULENCE",
      subtitle: "The World's Most Exclusive LGBTQ+ Travel Society",
      description: "Join an elite circle of 500 distinguished members accessing impossible experiences. Private islands, Michelin-starred chefs, and bespoke adventures that redefine luxury.",
      price: "$25,000 Annually",
      exclusivity: "Limited to 500 Members Worldwide"
    },
    {
      title: "BEYOND FIRST CLASS",
      subtitle: "Private Jets • Exclusive Resorts • Impossible Access",
      description: "Skip commercial flights entirely. Our fleet ensures complete privacy while our global network opens doors that remain closed to others.",
      price: "Unlimited Access",
      exclusivity: "Members Only Experiences"
    },
    {
      title: "WHITE-GLOVE CONCIERGE",
      subtitle: "24/7 Personal Travel Architects",
      description: "From securing impossible reservations to arranging private museum tours after hours. We make the extraordinary, effortless.",
      price: "Always Included",
      exclusivity: "Dedicated Service Team"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 8000);
    return () => clearInterval(timer);
  }, [slides.length]);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Sophisticated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
      
      {/* Luxury Pattern Overlay */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-40 h-40 border border-gold-400 rotate-45 animate-pulse"></div>
        <div className="absolute bottom-32 right-32 w-32 h-32 border border-gold-400 rotate-12"></div>
        <div className="absolute top-1/2 left-1/4 w-20 h-20 bg-gold-400 rounded-full opacity-10 animate-ping"></div>
        <div className="absolute top-1/3 right-1/4 w-16 h-16 border-2 border-gold-400 rounded-full opacity-20"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-4 max-w-7xl mx-auto">
        {/* Exclusivity Badge */}
        <div className="mb-8 animate-fade-in-up">
          <div className="inline-flex items-center px-8 py-3 bg-gold-400/10 border border-gold-400/30 rounded-full">
            <div className="w-2 h-2 bg-gold-400 rounded-full mr-3 animate-pulse"></div>
            <span className="text-gold-400 text-sm font-light tracking-[0.3em] uppercase">
              {slides[currentSlide].exclusivity}
            </span>
          </div>
        </div>

        {/* Main Title */}
        <div className="mb-8 overflow-hidden">
          <h1 className="text-6xl md:text-8xl lg:text-9xl font-bold leading-tight animate-fade-in-up animation-delay-200">
            <span className="bg-gradient-to-r from-white via-gold-200 to-gold-400 bg-clip-text text-transparent">
              {slides[currentSlide].title}
            </span>
          </h1>
        </div>

        {/* Subtitle */}
        <div className="mb-8 overflow-hidden">
          <h2 className="text-2xl md:text-4xl lg:text-5xl font-light text-gold-200 animate-fade-in-up animation-delay-400">
            {slides[currentSlide].subtitle}
          </h2>
        </div>

        {/* Description */}
        <div className="mb-12 overflow-hidden">
          <p className="text-xl md:text-2xl text-gray-300 max-w-5xl mx-auto leading-relaxed animate-fade-in-up animation-delay-600">
            {slides[currentSlide].description}
          </p>
        </div>

        {/* Price Display */}
        <div className="mb-12 animate-fade-in-up animation-delay-700">
          <div className="text-4xl md:text-5xl font-bold text-gold-400 mb-2">
            {slides[currentSlide].price}
          </div>
          <div className="text-gray-400 text-lg">
            Investment in Extraordinary Experiences
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-8 justify-center items-center animate-fade-in-up animation-delay-800">
          <Link 
            href="/apply-membership"
            className="group relative px-12 py-6 bg-gradient-to-r from-gold-400 to-gold-600 text-black font-bold text-xl rounded-none border-2 border-transparent hover:border-white transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
          >
            <span className="relative z-10">Apply for Membership</span>
            <div className="absolute inset-0 bg-gradient-to-r from-white to-gold-200 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
          </Link>
          
          <Link 
            href="/private-consultation"
            className="group px-12 py-6 border-2 border-gold-400 text-gold-400 font-bold text-xl rounded-none hover:bg-gold-400 hover:text-black transition-all duration-300 transform hover:scale-105"
          >
            Schedule Private Consultation
          </Link>
        </div>

        {/* Slide Indicators */}
        <div className="flex justify-center mt-16 space-x-4">
          {slides.map((_, index) => (
            <button
              key={index}
              type="button"
              onClick={() => setCurrentSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
              className={`w-4 h-4 rounded-full transition-all duration-300 ${
                index === currentSlide 
                  ? 'bg-gold-400 scale-125' 
                  : 'bg-white/30 hover:bg-white/50'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-8 h-12 border-2 border-gold-400/50 rounded-full flex justify-center">
          <div className="w-1 h-4 bg-gold-400/70 rounded-full mt-3 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}
