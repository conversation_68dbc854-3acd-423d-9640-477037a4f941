import Link from 'next/link';

export default function PrivateConsultation() {
  const consultationFeatures = [
    {
      icon: "🎯",
      title: "Personalized Assessment",
      description: "We'll understand your travel preferences, lifestyle, and expectations to ensure Octavia Opulence is the perfect fit."
    },
    {
      icon: "🗺️",
      title: "Custom Itinerary Preview",
      description: "Experience a sample of our bespoke planning with a personalized luxury itinerary designed just for you."
    },
    {
      icon: "🤝",
      title: "Network Introduction",
      description: "Meet select members of our community and understand the exclusive network you'll be joining."
    },
    {
      icon: "💎",
      title: "Membership Benefits Review",
      description: "Detailed walkthrough of all membership privileges, from private jets to exclusive properties."
    }
  ];

  const consultationStats = [
    { number: "90 min", label: "Consultation Duration" },
    { number: "1-on-1", label: "Personal Attention" },
    { number: "24hr", label: "Response Time" },
    { number: "100%", label: "Confidential" }
  ];

  return (
    <section className="py-24 bg-black">
      <div className="container mx-auto px-4">
        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Column - Content */}
          <div>
            <div className="inline-block bg-gold-400/10 border border-gold-400/30 px-6 py-2 rounded-full mb-6">
              <span className="text-gold-400 text-sm font-semibold tracking-wider uppercase">
                Private Consultation
              </span>
            </div>
            
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
              Begin Your Journey to
              <br />
              <span className="text-gold-400">Extraordinary</span>
            </h2>
            
            <p className="text-xl text-gray-300 leading-relaxed mb-8">
              Schedule a private, confidential consultation with our membership team. 
              Discover how Octavia Opulence can transform your travel experiences and 
              connect you with our exclusive global community.
            </p>

            {/* Features */}
            <div className="space-y-6 mb-10">
              {consultationFeatures.map((feature, index) => (
                <div key={index} className="flex items-start">
                  <div className="text-2xl mr-4 mt-1">{feature.icon}</div>
                  <div>
                    <h3 className="text-xl font-bold text-white mb-2">{feature.title}</h3>
                    <p className="text-gray-300">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6">
              <Link 
                href="/schedule-consultation"
                className="inline-flex items-center justify-center px-8 py-4 bg-gold-400 text-black font-bold text-lg rounded-none hover:bg-gold-300 transition-all duration-300 group"
              >
                Schedule Consultation
                <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
              
              <Link 
                href="/membership-brochure"
                className="inline-flex items-center justify-center px-8 py-4 border-2 border-gold-400 text-gold-400 font-bold text-lg rounded-none hover:bg-gold-400 hover:text-black transition-all duration-300"
              >
                Download Brochure
              </Link>
            </div>
          </div>

          {/* Right Column - Consultation Details */}
          <div className="bg-gradient-to-br from-gray-900 to-black p-8 border border-gold-400/20 rounded-none">
            <h3 className="text-3xl font-bold text-white mb-6 text-center">
              What to Expect
            </h3>
            
            {/* Stats */}
            <div className="grid grid-cols-2 gap-6 mb-8">
              {consultationStats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl font-bold text-gold-400 mb-1">{stat.number}</div>
                  <div className="text-gray-400 text-sm">{stat.label}</div>
                </div>
              ))}
            </div>

            {/* Process */}
            <div className="space-y-6">
              <div className="flex items-start">
                <div className="w-8 h-8 bg-gold-400 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                  <span className="text-black font-bold text-sm">1</span>
                </div>
                <div>
                  <h4 className="text-white font-semibold mb-1">Initial Discussion</h4>
                  <p className="text-gray-300 text-sm">Share your travel aspirations and lifestyle preferences</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="w-8 h-8 bg-gold-400 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                  <span className="text-black font-bold text-sm">2</span>
                </div>
                <div>
                  <h4 className="text-white font-semibold mb-1">Membership Overview</h4>
                  <p className="text-gray-300 text-sm">Detailed presentation of benefits and exclusive access</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="w-8 h-8 bg-gold-400 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                  <span className="text-black font-bold text-sm">3</span>
                </div>
                <div>
                  <h4 className="text-white font-semibold mb-1">Custom Proposal</h4>
                  <p className="text-gray-300 text-sm">Personalized membership proposal and next steps</p>
                </div>
              </div>
            </div>

            {/* Contact Info */}
            <div className="mt-8 pt-6 border-t border-gold-400/20">
              <div className="text-center">
                <p className="text-gray-400 text-sm mb-2">Prefer to speak directly?</p>
                <p className="text-gold-400 font-semibold">+1 (555) OCTAVIA</p>
                <p className="text-gray-400 text-xs">Available 24/7 for prospective members</p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section - Exclusivity Reminder */}
        <div className="text-center bg-gradient-to-r from-gold-400/10 to-gold-600/10 border border-gold-400/20 p-12 rounded-none">
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Membership is Limited
          </h3>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            With only 500 memberships available worldwide, Octavia Opulence maintains the exclusivity 
            and personalized service that defines our community.
          </p>
          <div className="flex flex-col sm:flex-row gap-8 justify-center items-center">
            <div className="text-center">
              <div className="text-4xl font-bold text-gold-400">127</div>
              <div className="text-gray-400">Current Members</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-gold-400">373</div>
              <div className="text-gray-400">Available Spots</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-gold-400">25%</div>
              <div className="text-gray-400">Acceptance Rate</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
