import Link from 'next/link';

export default function UltraLuxuryExperiences() {
  const experiences = [
    {
      id: 1,
      title: "Private Island Sanctuary",
      location: "Maldives",
      description: "Your own 50-acre private island with overwater villas, personal chef, and helicopter transfers. Complete privacy with world-class amenities.",
      exclusivity: "Members Only",
      duration: "7-14 Days",
      highlights: ["Private Island", "Helicopter Access", "Personal Chef", "Spa Services"],
      image: "🏝️"
    },
    {
      id: 2,
      title: "Château de Versailles After Dark",
      location: "France",
      description: "Exclusive after-hours access to Versailles with private dining in the Hall of Mirrors. Experience history like royalty.",
      exclusivity: "Ultra-Private",
      duration: "3 Days",
      highlights: ["Private Tours", "Michelin Dining", "Historical Access", "Royal Treatment"],
      image: "🏰"
    },
    {
      id: 3,
      title: "Antarctic Luxury Expedition",
      location: "Antarctica",
      description: "Private yacht expedition to the last frontier with world-class naturalists, gourmet cuisine, and heated observation decks.",
      exclusivity: "12 Guests Maximum",
      duration: "10 Days",
      highlights: ["Private Yacht", "Expert Guides", "Gourmet Cuisine", "Wildlife Encounters"],
      image: "🐧"
    },
    {
      id: 4,
      title: "Tokyo Imperial Experience",
      location: "Japan",
      description: "Private access to imperial gardens, exclusive sake tastings with master brewers, and traditional kaiseki with renowned chefs.",
      exclusivity: "Invitation Only",
      duration: "5 Days",
      highlights: ["Imperial Access", "Master Classes", "Cultural Immersion", "VIP Treatment"],
      image: "🏯"
    },
    {
      id: 5,
      title: "Sahara Desert Palace",
      location: "Morocco",
      description: "Luxury desert camp with air-conditioned tents, private oasis, and camel caravan experiences under the stars.",
      exclusivity: "Members Only",
      duration: "4 Days",
      highlights: ["Desert Palace", "Stargazing", "Cultural Experiences", "Luxury Camping"],
      image: "🐪"
    },
    {
      id: 6,
      title: "Northern Lights Private Lodge",
      location: "Iceland",
      description: "Exclusive glass igloos with heated floors, private aurora viewing, and geothermal spa experiences in complete privacy.",
      exclusivity: "Ultra-Private",
      duration: "6 Days",
      highlights: ["Glass Igloos", "Aurora Viewing", "Geothermal Spa", "Private Chef"],
      image: "🌌"
    }
  ];

  return (
    <section className="py-24 bg-black">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-block bg-gold-400/10 border border-gold-400/30 px-6 py-2 rounded-full mb-6">
            <span className="text-gold-400 text-sm font-semibold tracking-wider uppercase">
              Exclusive Experiences
            </span>
          </div>
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
            Impossible Made
            <br />
            <span className="text-gold-400">Effortless</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
            Access to the world's most coveted destinations and experiences, available exclusively to Octavia Opulence members.
          </p>
          <div className="inline-block bg-gradient-to-r from-gold-400/10 to-gold-600/10 border border-gold-400/20 px-8 py-3 rounded-full">
            <span className="text-gold-400 text-lg font-semibold">
              Valued at Over $2M Annually
            </span>
          </div>
        </div>

        {/* Experiences Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {experiences.map((experience) => (
            <div 
              key={experience.id}
              className="group bg-gradient-to-br from-gray-900 to-black rounded-none border border-gold-400/20 overflow-hidden hover:border-gold-400/50 transition-all duration-500 hover:transform hover:scale-105"
            >
              {/* Image/Icon Section */}
              <div className="h-64 bg-gradient-to-br from-gold-400/20 to-purple-900/30 relative overflow-hidden flex items-center justify-center">
                <div className="text-8xl opacity-50 group-hover:scale-110 transition-transform duration-300">
                  {experience.image}
                </div>
                <div className="absolute top-4 left-4 bg-gold-400 text-black px-3 py-1 rounded-none text-xs font-bold tracking-wider">
                  {experience.exclusivity}
                </div>
                <div className="absolute bottom-4 right-4 bg-black/70 text-gold-400 px-3 py-1 rounded-none text-xs font-semibold">
                  {experience.duration}
                </div>
              </div>

              {/* Content */}
              <div className="p-8">
                <div className="mb-4">
                  <h3 className="text-2xl font-bold text-white group-hover:text-gold-400 transition-colors mb-2">
                    {experience.title}
                  </h3>
                  <p className="text-gold-400 font-semibold">
                    {experience.location}
                  </p>
                </div>
                
                <p className="text-gray-300 leading-relaxed mb-6">
                  {experience.description}
                </p>

                {/* Highlights */}
                <div className="mb-6">
                  <div className="flex flex-wrap gap-2">
                    {experience.highlights.map((highlight, index) => (
                      <span 
                        key={index}
                        className="bg-gold-400/10 border border-gold-400/30 text-gold-400 px-3 py-1 rounded-full text-xs font-medium"
                      >
                        {highlight}
                      </span>
                    ))}
                  </div>
                </div>

                {/* CTA */}
                <Link 
                  href={`/experiences/${experience.id}`}
                  className="inline-flex items-center text-gold-400 font-semibold hover:text-white transition-colors group"
                >
                  Reserve Experience
                  <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <Link 
            href="/exclusive-portfolio"
            className="inline-flex items-center px-12 py-4 border-2 border-gold-400 text-gold-400 font-bold text-lg rounded-none hover:bg-gold-400 hover:text-black transition-all duration-300 group"
          >
            View Complete Portfolio
            <svg className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
}
